// TextureLoader.cs - Optimized for millions of notes without blending
using Raylib_cs;
using System.Collections.Concurrent;
using System.Numerics;

namespace SharpMIDI.Renderer
{
    public static class TextureLoader
    {
        public struct TextureSegment
        {
            public RenderTexture2D texture;
            public float startTime;
            public float endTime;
            public float pixelsPerMs;
            public int width;
            public bool isLoaded;
            public bool isLoading;
            public long lastAccessTime;
            public TaskCompletionSource<bool>? loadingTask;
        }

        private struct PrecompiledSegment
        {
            public int index;
            public TextureSegment segment;
            public byte[] pixelData; // RGBA bitmap data
            public int width;
            public int height;
        }

        private const int MAX_TEXTURE_WIDTH = 16384;
        //private const int MAX_LOADED_SEGMENTS = 10; FUCK limitations
        private const float SEGMENT_PRELOAD_TIME = 800f; // Reduced preload time
        private const int PAD = 55;
        private const int NOTES_PER_BATCH = 15000; // Increased batch size for better throughput
        private const int MAX_CONCURRENT_LOADS = 2; // Reduced to save memory
        private const int GLOW_SPATIAL_GRID_SIZE = 1000; // Grid size for spatial indexing (ms)

        private static TextureSegment[] textureSegments = Array.Empty<TextureSegment>();
        private static Dictionary<int, TextureCompiler.EnhancedNote[]> segmentNoteCache = new();
        private static Dictionary<int, List<TextureCompiler.EnhancedNote>> glowSpatialGrid = new(); // Spatial index for glow
        private static readonly SemaphoreSlim loadingSemaphore = new(MAX_CONCURRENT_LOADS);
        private static readonly ConcurrentQueue<int> loadingQueue = new();
        private static readonly ConcurrentQueue<PrecompiledSegment> readyToUpload = new();
        private static long accessCounter = 0;
        private static CancellationTokenSource? cancellationTokenSource;

        public static int LoadedSegmentCount => textureSegments.Count(s => s.isLoaded);
        public static int TotalSegmentCount => textureSegments.Length;

        public static float CreateTextureSegments(TextureCompiler.EnhancedNote[] allNotes, float totalDuration, float ppm)
        {
            // Cancel any ongoing loading operations
            cancellationTokenSource?.Cancel();
            cancellationTokenSource = new CancellationTokenSource();

            float segmentDuration = MAX_TEXTURE_WIDTH / ppm;
            int count = (int)Math.Ceiling(totalDuration / segmentDuration);
            if (count == 1)
            {
                segmentDuration = totalDuration;
                ppm = Math.Min(ppm, MAX_TEXTURE_WIDTH / totalDuration);
            }

            textureSegments = Enumerable.Range(0, count).Select(i =>
            {
                float start = i * segmentDuration;
                float end = Math.Min(totalDuration, (i + 1) * segmentDuration);
                int width = (int)Math.Ceiling((end - start) * ppm);
                return new TextureSegment
                {
                    startTime = start,
                    endTime = end,
                    pixelsPerMs = ppm,
                    width = width,
                    isLoaded = false,
                    isLoading = false
                };
            }).ToArray();
            return segmentDuration;
        }

        public static void PrepareSegmentNoteCache(TextureCompiler.EnhancedNote[] allNotes)
        {
            segmentNoteCache.Clear();
            glowSpatialGrid.Clear();
            
            // Build spatial grid for glow effect (much faster than iterating all notes)
            BuildGlowSpatialGrid(allNotes);
            
            // Use spatial indexing for faster lookups with millions of notes
            var notesByStartTime = allNotes.OrderBy(n => n.startTime).ToArray();
            
            Parallel.For(0, textureSegments.Length, i =>
            {
                var seg = textureSegments[i];
                
                // Binary search for performance with large datasets
                int startIdx = BinarySearchStart(notesByStartTime, seg.startTime);
                int endIdx = BinarySearchEnd(notesByStartTime, seg.endTime);
                
                var segmentNotes = new List<TextureCompiler.EnhancedNote>(endIdx - startIdx);
                for (int j = startIdx; j < endIdx; j++)
                {
                    var note = notesByStartTime[j];
                    if (note.endTime > seg.startTime && note.startTime < seg.endTime)
                        segmentNotes.Add(note);
                }
                
                lock (segmentNoteCache) 
                    segmentNoteCache[i] = segmentNotes.ToArray();
            });
        }

        private static void BuildGlowSpatialGrid(TextureCompiler.EnhancedNote[] allNotes)
        {
            // Build spatial grid for fast glow lookups
            foreach (var note in allNotes)
            {
                int startGrid = (int)(note.startTime / GLOW_SPATIAL_GRID_SIZE);
                int endGrid = (int)(note.endTime / GLOW_SPATIAL_GRID_SIZE);
                
                // Add note to all grid cells it spans
                for (int gridIdx = startGrid; gridIdx <= endGrid; gridIdx++)
                {
                    if (!glowSpatialGrid.TryGetValue(gridIdx, out var gridNotes))
                    {
                        gridNotes = new List<TextureCompiler.EnhancedNote>();
                        glowSpatialGrid[gridIdx] = gridNotes;
                    }
                    gridNotes.Add(note);
                }
            }
        }

        private static int BinarySearchStart(TextureCompiler.EnhancedNote[] notes, float targetTime)
        {
            int left = 0, right = notes.Length;
            while (left < right)
            {
                int mid = (left + right) / 2;
                if (notes[mid].endTime <= targetTime)
                    left = mid + 1;
                else
                    right = mid;
            }
            return Math.Max(0, left - 100); // Small buffer for overlapping notes
        }

        private static int BinarySearchEnd(TextureCompiler.EnhancedNote[] notes, float targetTime)
        {
            int left = 0, right = notes.Length;
            while (left < right)
            {
                int mid = (left + right) / 2;
                if (notes[mid].startTime < targetTime)
                    left = mid + 1;
                else
                    right = mid;
            }
            return Math.Min(notes.Length, left + 100); // Small buffer for overlapping notes
        }

        public static void ManageTextureMemory(float currentTick)
        {
            if (textureSegments.Length == 0) return;

            float window = GetWindow();
            float halfWindow = window * 0.5f;
            float viewportStart = currentTick - halfWindow;
            float viewportEnd = currentTick + halfWindow + SEGMENT_PRELOAD_TIME;

            var neededSegments = new HashSet<int>();

            // Determine which segments are needed for current viewport
            for (int i = 0; i < textureSegments.Length; i++)
            {
                var s = textureSegments[i];
                if (s.endTime > viewportStart && s.startTime < viewportEnd)
                    neededSegments.Add(i);
            }

            // Aggressive cleanup: Unload segments that are far from viewport first
            float cleanupMargin = SEGMENT_PRELOAD_TIME * 2f; // Cleanup segments beyond 2x preload time
            for (int i = 0; i < textureSegments.Length; i++)
            {
                var s = textureSegments[i];
                if (s.isLoaded && !s.isLoading)
                {
                    // Unload if segment is far outside viewport
                    if (s.endTime < (currentTick - halfWindow - cleanupMargin) || 
                        s.startTime > (currentTick + halfWindow + cleanupMargin))
                    {
                        UnloadSegment(i);
                        continue;
                    }
                    
                    // Also unload if not immediately needed
                    if (!neededSegments.Contains(i))
                    {
                        UnloadSegment(i);
                    }
                }
            }

            // Count currently loaded segments
            int loadedCount = 0;
            var loadedSegments = new List<(int index, long lastAccess)>();
            long now = ++accessCounter;

            for (int i = 0; i < textureSegments.Length; i++)
            {
                var s = textureSegments[i];
                if (!s.isLoaded) continue;
                
                loadedCount++;
                if (neededSegments.Contains(i))
                {
                    textureSegments[i].lastAccessTime = now;
                    loadedSegments.Add((i, now));
                }
                else
                {
                    loadedSegments.Add((i, s.lastAccessTime));
                }
            }

            // Enforce strict segment limit with priority for needed segments
            /*if (loadedCount > MAX_LOADED_SEGMENTS)
            {
                loadedSegments.Sort((a, b) => a.lastAccess.CompareTo(b.lastAccess));
                int toUnload = loadedCount - MAX_LOADED_SEGMENTS;

                for (int i = 0; i < toUnload && i < loadedSegments.Count; i++)
                {
                    int index = loadedSegments[i].index;
                    if (!neededSegments.Contains(index) && !textureSegments[index].isLoading)
                        UnloadSegment(index);
                }
            }*/

            // Load needed segments (but don't exceed our limit)
            int currentLoaded = textureSegments.Count(s => s.isLoaded);
            int currentLoading = textureSegments.Count(s => s.isLoading);
            
            foreach (int index in neededSegments)
            {
                if (!textureSegments[index].isLoaded && !textureSegments[index].isLoading)
                {
                    // no. no limits. nononononnononno
                    //if (currentLoaded + currentLoading >= MAX_LOADED_SEGMENTS) break;
                    
                    textureSegments[index].isLoading = true;
                    textureSegments[index].loadingTask = new TaskCompletionSource<bool>();
                    loadingQueue.Enqueue(index);
                    currentLoading++;
                    
                    // Start async loading without blocking
                    _ = Task.Run(() => LoadSegmentAsync(index, cancellationTokenSource.Token));
                }
            }
        }

        private static async Task LoadSegmentAsync(int index, CancellationToken cancellationToken)
        {
            if (index < 0 || index >= textureSegments.Length) return;
            
            try
            {
                await loadingSemaphore.WaitAsync(cancellationToken);
                
                if (cancellationToken.IsCancellationRequested) return;
                
                var segment = textureSegments[index];
                if (segment.isLoaded) return;

                // Step 1: Precompile to bitmap on background thread (heavy work)
                var precompiled = await PrecompileSegmentToBitmap(index, segment, cancellationToken);
                
                if (cancellationToken.IsCancellationRequested)
                    return;

                // Step 2: Queue for fast GPU upload on main thread
                readyToUpload.Enqueue(precompiled);
            }
            catch (OperationCanceledException)
            {
                // Expected when cancellation is requested
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error loading segment {index}: {ex.Message}");
                textureSegments[index].isLoading = false;
                textureSegments[index].loadingTask?.SetResult(false);
            }
            finally
            {
                loadingSemaphore.Release();
            }
        }

        private static async Task<PrecompiledSegment> PrecompileSegmentToBitmap(int index, TextureSegment segment, CancellationToken cancellationToken)
        {
            return await Task.Run(async () =>
            {
                // Get notes for this segment
                if (!segmentNoteCache.TryGetValue(index, out var notes))
                {
                    notes = TextureCompiler.AllNotes
                        .Where(n => n.endTime > segment.startTime && n.startTime < segment.endTime)
                        .ToArray();
                    
                    lock (segmentNoteCache)
                        segmentNoteCache[index] = notes;
                }

                int height = Raylib.GetScreenHeight();
                
                // Handle empty segments
                if (notes.Length == 0)
                {
                    return new PrecompiledSegment
                    {
                        index = index,
                        segment = segment,
                        pixelData = null, // Empty segment - will be cleared
                        width = segment.width,
                        height = height
                    };
                }

                // Precompile bitmap data on background thread
                byte[] pixels = new byte[segment.width * height * 4]; // RGBA
                
                const int topPad = PAD / 3;
                const int bottomPad = PAD - 4 - topPad;
                float yscale = (height - topPad - bottomPad) / 128f;

                // Sort notes by start time only for better cache performance
                var sortedNotes = notes.OrderBy(n => n.startTime).ToArray();

                // Process notes in smaller batches to reduce memory pressure and allow cancellation
                int batchSize = Math.Min(NOTES_PER_BATCH, sortedNotes.Length);
                
                for (int batchStart = 0; batchStart < sortedNotes.Length; batchStart += batchSize)
                {
                    if (cancellationToken.IsCancellationRequested) break;
                    
                    int batchEnd = Math.Min(batchStart + batchSize, sortedNotes.Length);
                    
                    // Process batch with simple overwrite (no blending)
                    for (int i = batchStart; i < batchEnd; i++)
                    {
                        var note = sortedNotes[i];
                        
                        byte r = (byte)(note.color >> 16);
                        byte g = (byte)(note.color >> 8);
                        byte b = (byte)(note.color);
                        byte a = 255; // Full opacity, no alpha blending

                        int y = (int)(height - bottomPad - note.noteNumber * yscale);
                        int noteHeight = note.height;

                        float start = Math.Max(note.startTime, segment.startTime);
                        float end = Math.Min(note.endTime, segment.endTime);
                        int x1 = (int)((start - segment.startTime) * segment.pixelsPerMs);
                        int x2 = (int)((end - segment.startTime) * segment.pixelsPerMs);
                        int width = Math.Max(1, x2 - x1);

                        // Simple rectangle fill - no blending logic
                        FillRectangleSimple(pixels, segment.width, height, x1, y, width, noteHeight, r, g, b, a);
                    }
                    
                    // Yield control occasionally during long processing
                    if (batchStart % (batchSize * 3) == 0)
                        Task.Yield();
                }

                return new PrecompiledSegment
                {
                    index = index,
                    segment = segment,
                    pixelData = pixels,
                    width = segment.width,
                    height = height
                };
            }, cancellationToken);
        }

        // Simplified rectangle fill without any blending logic
        private static unsafe void FillRectangleSimple(byte[] pixels, int bufferWidth, int bufferHeight, 
            int x, int y, int width, int height, byte r, byte g, byte b, byte a)
        {
            // Clamp to buffer bounds
            if (x >= bufferWidth || y >= bufferHeight || x + width <= 0 || y + height <= 0) return;
            
            int startX = Math.Max(0, x);
            int startY = Math.Max(0, y);
            int endX = Math.Min(bufferWidth, x + width);
            int endY = Math.Min(bufferHeight, y + height);

            fixed (byte* ptr = pixels)
            {
                // Pre-calculate pixel value as 32-bit integer for faster writes
                uint pixelValue = (uint)(a << 24 | b << 16 | g << 8 | r);
                
                for (int py = startY; py < endY; py++)
                {
                    // Calculate row start once
                    int rowStart = py * bufferWidth;
                    
                    // Use pointer arithmetic for faster pixel writes
                    uint* rowPtr = (uint*)(ptr + rowStart * 4);
                    
                    for (int px = startX; px < endX; px++)
                    {
                        // Direct 32-bit write - much faster than 4 separate byte writes
                        rowPtr[px] = pixelValue;
                    }
                }
            }
        }

        private static void ProcessMainThreadOperations()
        {
            const int maxOpsPerFrame = 2; // Reduced to be more conservative with memory
            int processed = 0;

            // Process precompiled segments (fast texture upload)
            while (readyToUpload.TryDequeue(out var precompiled) && processed < maxOpsPerFrame)
            {
                UploadPrecompiledSegment(precompiled);
                processed++;
            }
        }

        private static void UploadPrecompiledSegment(PrecompiledSegment precompiled)
        {
            if (precompiled.index < 0 || precompiled.index >= textureSegments.Length) return;
            if (textureSegments[precompiled.index].isLoaded) return;

            var texture = GetReusableTexture(precompiled.width);
            
            if (precompiled.pixelData != null)
            {
                // Upload the precompiled bitmap directly to GPU - this is very fast!
                unsafe
                {
                    fixed (byte* ptr = precompiled.pixelData)
                    {
                        Raylib.UpdateTexture(texture.Texture, ptr);
                    }
                }
            }
            else
            {
                // Empty segment - just clear it
                Raylib.BeginTextureMode(texture);
                Raylib.ClearBackground(Raylib_cs.Color.Black);
                Raylib.EndTextureMode();
            }
            
            // Atomically update the segment
            lock (textureSegments)
            {
                textureSegments[precompiled.index].texture = texture;
                textureSegments[precompiled.index].isLoaded = true;
                textureSegments[precompiled.index].isLoading = false;
                textureSegments[precompiled.index].lastAccessTime = ++accessCounter;
                textureSegments[precompiled.index].loadingTask?.SetResult(true);
            }
        }

        private static void UnloadSegment(int index)
        {
            if (index < 0 || index >= textureSegments.Length) return;
            ref var segment = ref textureSegments[index];
            if (!segment.isLoaded) return;

            // Actually free the texture instead of storing it
            if (segment.texture.Id != 0)
            {
                Raylib.UnloadRenderTexture(segment.texture);
            }

            segment.texture = default;
            segment.isLoaded = false;
            segment.isLoading = false;
            segment.lastAccessTime = 0;
            segment.loadingTask = null;
        }

        public static void DrawScrollingTextures(float tick, int W, int H, float window)
        {
            if (!TextureRenderer.ready || textureSegments.Length == 0) return;

            // Process precompiled segments on main thread (very fast)
            ProcessMainThreadOperations();

            float halfWindow = window * 0.5f;
            float viewStart = tick - halfWindow;
            float viewEnd = tick + halfWindow;

            for (int i = 0; i < textureSegments.Length; i++)
            {
                var seg = textureSegments[i];
                if (!seg.isLoaded || seg.endTime <= viewStart || seg.startTime >= viewEnd)
                    continue;

                float visibleStart = Math.Max(viewStart, seg.startTime);
                float visibleEnd = Math.Min(viewEnd, seg.endTime);

                float srcX = (visibleStart - seg.startTime) * seg.pixelsPerMs;
                float srcW = (visibleEnd - visibleStart) * seg.pixelsPerMs;

                if (srcW <= 0) continue;

                float dstX = (visibleStart - viewStart) / window * W;
                float dstW = (visibleEnd - visibleStart) / window * W;

                Raylib_cs.Rectangle src = new()
                {
                    X = srcX,
                    Y = 0,
                    Width = srcW,
                    Height = H 
                };

                Raylib_cs.Rectangle dst = new()
                {
                    X = dstX,
                    Y = 0,
                    Width = dstW,
                    Height = H
                };

                Raylib.DrawTexturePro(seg.texture.Texture, src, dst, Vector2.Zero, 0f, Raylib_cs.Color.White);
            }
        }

        public static void DrawGlowEffect(float tick, int W, int H, float window)
        {
            if (!TextureRenderer.ready || glowSpatialGrid.Count == 0) return;

            float viewStart = tick - window * 0.5f;
            float viewEnd = tick + window * 0.5f;
            float scale = W / window;

            const int topPad = PAD / 3;
            const int bottomPad = PAD - 4 - topPad;
            float yscale = (H - topPad - bottomPad) / 128f;

            // Calculate which grid cells overlap with current viewport
            int startGrid = Math.Max(0, (int)(viewStart / GLOW_SPATIAL_GRID_SIZE));
            int endGrid = (int)(viewEnd / GLOW_SPATIAL_GRID_SIZE);

            // Only process notes in visible grid cells - HUGE performance boost!
            for (int gridIdx = startGrid; gridIdx <= endGrid; gridIdx++)
            {
                if (!glowSpatialGrid.TryGetValue(gridIdx, out var gridNotes)) continue;

                foreach (var note in gridNotes)
                {
                    // Quick bounds check - skip notes outside current time
                    if (tick < note.startTime || tick > note.endTime) continue;

                    float x1 = (note.startTime - viewStart) * scale;
                    float x2 = (note.endTime - viewStart) * scale;

                    // Quick screen bounds check
                    if (x2 <= 0 || x1 >= W) continue;

                    x1 = MathF.Max(0, x1);
                    x2 = MathF.Min(W, x2);
                    if (x2 <= x1) continue;

                    uint c = note.color;
                    Raylib_cs.Color glow = new()
                    {
                        R = (byte)Math.Min(255, ((c >> 14) & 0x3FC)),
                        G = (byte)Math.Min(255, ((c >> 6) & 0x3FC)),
                        B = (byte)Math.Min(255, ((c << 2) & 0x3FC)),
                        A = 180
                    };

                    float y = H - bottomPad - note.noteNumber * yscale;
                    Raylib.DrawRectangle((int)x1, (int)y - 1, (int)(x2 - x1), note.height + 2, glow);
                }
            }
        }

        public static void CleanupTextures()
        {
            cancellationTokenSource?.Cancel();
            
            // Clear queues
            while (readyToUpload.TryDequeue(out _)) { }
            while (loadingQueue.TryDequeue(out _)) { }
            
            // Properly unload all segment textures
            foreach (var segment in textureSegments)
            {
                if (segment.texture.Id != 0)
                    Raylib.UnloadRenderTexture(segment.texture);
            }

            textureSegments = Array.Empty<TextureSegment>();
            segmentNoteCache.Clear();
            glowSpatialGrid.Clear(); // Clear spatial grid
            
            // Force garbage collection to free memory immediately
            GC.Collect();
            GC.WaitForPendingFinalizers();
            
            TextureRenderer.ready = false;
        }

        private static RenderTexture2D GetReusableTexture(int width)
        {
            // Always create new textures since we're now properly freeing them
            return Raylib.LoadRenderTexture(width, Raylib.GetScreenHeight());
        }

        private static float GetWindow() => typeof(TextureRenderer).GetField("window", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Static) is { } f ? (float)f.GetValue(null)! : 2000f;
    }
}