// TextureRenderer.cs
using Raylib_cs;
using System.Numerics;
using SharpMIDI;

namespace SharpMIDI.Renderer
{
    public static class TextureRenderer
    {
        private const int W = 1280, H = 720;
        private static bool glow = false, vsync = true, debug = false;
        private static float window = 2000f;

        public static bool ready;
        public static bool run;

        public static void StartRenderer()
        {
            if (run) return;
            run = true;
            Task.Run(RenderLoop);
        }

        public static void StopRenderer() => run = false;

        private static void RenderLoop()
        {
            Raylib.InitWindow(W, H, "SharpMIDI - Streaming Renderer");
            Raylib.SetTargetFPS(vsync ? Raylib.GetMonitorRefreshRate(Raylib.GetCurrentMonitor()) : 0);

            while (!Raylib.WindowShouldClose() && run)
            {
                float tick = (float)MIDIClock.GetTick();
                HandleInput();

                if (ready)
                {
                    TextureLoader.ManageTextureMemory(tick);
                }

                Raylib.BeginDrawing();
                Raylib.ClearBackground(Raylib_cs.Color.Black);
                TextureLoader.DrawScrollingTextures(tick, W, H, window);
                Raylib.DrawLine(W >> 1, 0, W >> 1, H, Raylib_cs.Color.Red);
                if (glow && ready) TextureLoader.DrawGlowEffect(tick, W, H, window);
                DrawUI(tick);
                Raylib.EndDrawing();
            }

            TextureLoader.CleanupTextures();
            Raylib.CloseWindow();
            run = false;
        }

        private static void HandleInput()
        {
            if (Raylib.IsKeyPressed(KeyboardKey.Up) || Raylib.IsKeyPressedRepeat(KeyboardKey.Up))
                window = Math.Max(100f, window - (window * 0.1f));

            if (Raylib.IsKeyPressed(KeyboardKey.Down) || Raylib.IsKeyPressedRepeat(KeyboardKey.Down))
                window = Math.Min(100000f, window + (window * 0.1f));

            if (Raylib.IsKeyPressed(KeyboardKey.G)) glow = !glow;
            if (Raylib.IsKeyPressed(KeyboardKey.D)) debug = !debug;

            if (Raylib.IsKeyPressed(KeyboardKey.V))
            {
                vsync = !vsync;
                Raylib.SetTargetFPS(vsync ? Raylib.GetMonitorRefreshRate(Raylib.GetCurrentMonitor()) : 0);
            }

            if (Raylib.IsKeyPressed(KeyboardKey.Escape)) run = false;
        }

        private static void DrawUI(float tick)
        {
            int loaded = TextureLoader.LoadedSegmentCount;
            int total = TextureLoader.TotalSegmentCount;

            string status = ready ? $"STREAMING ({loaded}/{total} Segments)" : "LOADING...";

            Raylib.DrawText(
                $"Tick: {tick:F0} | Tempo: {MIDIPlayer.tempo:F1} | Zoom: {window:F0} | Glow: {(glow ? "ON" : "OFF")} | FPS: {Raylib.GetFPS()}",
                10, 5, 16, Raylib_cs.Color.Green);

            if (debug)
            {
                Raylib.DrawText(
                    $"Status: {status} | Memory: {Form1.toMemoryText(GC.GetTotalMemory(false))}",
                    10, 25, 16, Raylib_cs.Color.SkyBlue);
            }

            string bottomText = ready ? Starter.filename : "No MIDI loaded.";
            Raylib_cs.Color color = ready ? Raylib_cs.Color.SkyBlue : Raylib_cs.Color.Yellow;
            Raylib.DrawText(bottomText, 10, H - 20, 16, color);
        }
    }
}