# Scrolling Texture System for Kiva MIDI

## Overview

The scrolling texture system provides efficient note rendering by pre-rendering notes to a large texture that scrolls continuously, similar to how TextureRenderer.cs, TextureLoader.cs, and TextureCompiler.cs work. This approach is much more efficient than rendering individual notes every frame.

## Key Features

- **Wraparound Scrolling**: Uses a 16,384-pixel tall texture that wraps around when it reaches the keyboard
- **Efficient Rendering**: Notes are rendered to texture once and then scrolled, reducing CPU/GPU load
- **Automatic Synchronization**: Integrates with existing MIDI time tracking and speed changes
- **Seamless Integration**: Works alongside existing note.png texture loading system

## Architecture

### Core Components

1. **RaylibRenderer** - Enhanced with scrolling texture support
   - `scrollingTexture`: 16K pixel tall RenderTexture2D for note rendering
   - `pixelsPerSecond`: Controls scrolling speed (default: 100 pixels/second)
   - `pendingNotes`: Buffer for notes to be rendered to texture

2. **RaylibMIDIRenderer** - Updated to handle time synchronization
   - Subscribes to `SpeedChanged` and `TimeChanged` events
   - Automatically resets texture on seeking or speed changes
   - Updates scrolling speed based on playback speed

### Key Methods

#### RaylibRenderer

- `InitializeScrollingTexture()`: Creates the 16K tall texture
- `UpdateScrollingTexture()`: Renders pending notes to texture
- `DrawScrollingTexture()`: Displays the scrolling texture with wraparound
- `SetScrollingSpeed(float)`: Updates pixels per second
- `ResetScrollingTexture()`: Clears texture (used on seeking/speed changes)
- `SetCurrentMIDITime(float)`: Updates current time for scrolling sync

#### RaylibMIDIRenderer

- `OnSpeedChanged()`: Resets texture and updates scrolling speed
- `OnTimeChanged()`: Resets texture on seeking

## Usage

The system is automatically initialized when `RaylibRenderer.InitializeNoteTexture()` is called. No additional setup is required.

### Automatic Features

1. **Speed Synchronization**: When playback speed changes, the scrolling speed automatically adjusts
2. **Seek Handling**: When seeking to a different time, the texture is automatically cleared and rebuilt
3. **Wraparound**: Notes seamlessly wrap around the 16K texture height
4. **Fallback**: If scrolling texture fails, falls back to individual note rendering

### Configuration

```csharp
// Set custom scrolling speed (pixels per second)
renderer.SetScrollingSpeed(200f); // 200 pixels per second

// Reset texture manually (if needed)
renderer.ResetScrollingTexture();

// Update current time manually (usually automatic)
renderer.SetCurrentMIDITime(currentTimeInSeconds);
```

## Technical Details

### Texture Specifications

- **Size**: Screen width × 16,384 pixels
- **Format**: RGBA render texture
- **Wraparound**: Y coordinates wrap using modulo operation
- **Memory**: Automatically managed by Raylib

### Rendering Process

1. **Note Addition**: Notes added via `AddNote()` or `AddNoteByKey()` go to both immediate buffer and pending buffer
2. **Texture Update**: `UpdateScrollingTexture()` renders pending notes to the large texture
3. **Scrolling Display**: `DrawScrollingTexture()` shows the appropriate portion with wraparound
4. **Time Sync**: Current MIDI time determines which part of texture to display

### Performance Benefits

- **Reduced Draw Calls**: Thousands of individual note rectangles become a single texture draw
- **GPU Efficiency**: Texture scrolling is handled by GPU hardware acceleration
- **Memory Efficient**: 16K texture reused continuously rather than storing all notes
- **Smooth Scrolling**: Hardware-accelerated texture sampling provides smooth movement

## Integration with Existing Systems

### Note Texture Loading

The scrolling system works alongside the existing note.png texture system:
- If note.png is available, it's used for rendering notes to the scrolling texture
- If not available, falls back to gradient rectangle rendering
- Same visual quality maintained

### MIDI Time Tracking

Integrates seamlessly with existing `PlayingState` system:
- Automatically subscribes to speed and time change events
- Uses existing `time.GetTime()` for synchronization
- Handles pausing, seeking, and speed changes automatically

### Settings Integration

Respects existing settings:
- Uses same keyboard layout calculations
- Honors note color settings
- Maintains visual consistency with original rendering

## Troubleshooting

### Common Issues

1. **Texture Not Created**: Check that `InitializeScrollingTexture()` is called after Raylib initialization
2. **No Scrolling**: Verify `SetCurrentMIDITime()` is being called with current playback time
3. **Performance Issues**: Ensure texture size is appropriate for screen resolution

### Debug Information

The system logs key events:
- Texture creation success/failure
- Scrolling speed changes
- Texture resets on seeking

### Fallback Behavior

If scrolling texture fails:
- System automatically falls back to individual note rendering
- No visual difference to user
- Performance may be reduced but functionality maintained
