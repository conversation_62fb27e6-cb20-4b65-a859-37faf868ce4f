// TextureCompiler.cs - Optimized for millions of notes
using System.Collections.Concurrent;
using System.Runtime.CompilerServices;
using SharpMIDI;

namespace SharpMIDI.Renderer
{
    public static class TextureCompiler
    {
        public struct EnhancedNote
        {
            public float startTime, endTime;
            public int noteNumber;
            public byte velocity, height;
            public uint color;
            public int trackIndex;
            public byte channel; // Add channel information
        }

        private const uint BK = 0x54A;
        private const float OPTIMAL_PIXELS_PER_MS = 2.0f;
        private const int INITIAL_CAPACITY = 100000; // Pre-allocate for better performance

        private static readonly byte[] noteHeights = new byte[128]; 
        private static readonly uint[] trackColors = new uint[256];
        
        // Use object pools for better memory management with millions of notes
        private static readonly ObjectPool<List<EnhancedNote>> noteListPool = new(() => new List<EnhancedNote>(1000));
        private static readonly ObjectPool<Dictionary<int, (float, byte)>> activeDictPool = new(() => new Dictionary<int, (float, byte)>(128));
        private static readonly ObjectPool<List<EnhancedNote>> resultListPool = new(() => new List<EnhancedNote>(INITIAL_CAPACITY));

        public static EnhancedNote[] AllNotes { get; private set; } = Array.Empty<EnhancedNote>();
        public static float TotalDuration { get; private set; }
        public static float SegmentDuration { get; private set; }

        static TextureCompiler()
        {
            for (int i = 0; i < 128; i++)
                noteHeights[i] = (byte)(((BK >> (i % 12)) & 1) != 0 ? 6 : 12);

            const float G = 1.618034f;
            for (int i = 0; i < 256; i++)
            {
                float h = (i * G * 360f) % 360f, c = 0.72f;
                float x = c * (1f - MathF.Abs((h / 60f) % 2f - 1f));
                int s = (int)(h / 60f) % 6;
                float r = 0, g = 0, b = 0;
                switch (s)
                {
                    case 0: r = c; g = x; break;
                    case 1: r = x; g = c; break;
                    case 2: g = c; b = x; break;
                    case 3: g = x; b = c; break;
                    case 4: r = x; b = c; break;
                    default: r = c; b = x; break;
                }
                r += 0.18f; g += 0.18f; b += 0.18f;
                trackColors[i] = 0xFF000000 | ((uint)(r * 255) << 16) | ((uint)(g * 255) << 8) | (uint)(b * 255);
            }
        }

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static EnhancedNote[] ProcessTrack(int trackIndex, dynamic track)
        {
            var noteList = noteListPool.Get();
            var activeNotes = activeDictPool.Get();
            
            try
            {
                noteList.Clear(); 
                activeNotes.Clear();

                float time = 0f;
                uint trackColor = trackColors[trackIndex & 255];
                
                // Process events with minimal allocations
                foreach (var ev in track.synthEvents)
                {
                    time += ev.pos;
                    int status = ev.val & 0xF0, ch = ev.val & 0x0F;
                    int note = (ev.val >> 8) & 0x7F, vel = (ev.val >> 16) & 0x7F;
                    
                    if (note > 127) continue;
                    
                    int key = (ch << 7) | note;
                    
                    if (status == 0x90 && vel > 0) 
                    {
                        activeNotes[key] = (time, (byte)vel);
                    }
                    else if (status == 0x80 || (status == 0x90 && vel == 0))
                    {
                        if (activeNotes.Remove(key, out var info))
                        {
                            noteList.Add(new EnhancedNote
                            {
                                startTime = info.Item1,
                                endTime = time,
                                noteNumber = note,
                                velocity = info.Item2,
                                height = noteHeights[note],
                                color = trackColor,
                                trackIndex = trackIndex,
                                channel = (byte)ch
                            });
                        }
                    }
                }

                // Handle hanging notes
                foreach (var kvp in activeNotes)
                {
                    int note = kvp.Key & 127;
                    int ch = (kvp.Key >> 7) & 15;
                    noteList.Add(new EnhancedNote
                    {
                        startTime = kvp.Value.Item1,
                        endTime = time + 100f,
                        noteNumber = note,
                        velocity = kvp.Value.Item2,
                        height = noteHeights[note],
                        color = trackColor,
                        trackIndex = trackIndex,
                        channel = (byte)ch
                    });
                }

                return noteList.ToArray();
            }
            finally
            {
                noteListPool.Return(noteList);
                activeDictPool.Return(activeNotes);
            }
        }

        public static void EnhanceTracksForRendering()
        {
            TextureRenderer.ready = false;
            TextureLoader.CleanupTextures();

            var validTracks = MIDIPlayer.tracks
                ?.Select((track, index) => (track, index))
                .Where(t => t.track?.synthEvents.Count > 0)
                .ToList();

            if (validTracks == null || validTracks.Count == 0)
            {
                AllNotes = Array.Empty<EnhancedNote>();
                TextureRenderer.ready = true;
                return;
            }

            // Use a result list pool for better memory management
            var resultList = resultListPool.Get();
            
            try
            {
                resultList.Clear();
                
                // Process tracks in parallel with controlled concurrency
                var parallelOptions = new ParallelOptions 
                { 
                    MaxDegreeOfParallelism = Math.Min(Environment.ProcessorCount, validTracks.Count)
                };
                
                var trackResults = new ConcurrentBag<EnhancedNote[]>();
                
                Parallel.ForEach(validTracks, parallelOptions, t =>
                {
                    var notes = ProcessTrack(t.index, t.track);
                    if (notes.Length > 0) 
                        trackResults.Add(notes);
                });

                // Estimate capacity to avoid reallocations
                int estimatedCapacity = trackResults.Sum(arr => arr.Length);
                if (resultList.Capacity < estimatedCapacity)
                    resultList.Capacity = estimatedCapacity;

                // Combine results efficiently
                foreach (var trackNotes in trackResults)
                {
                    resultList.AddRange(trackNotes);
                }

                // Sort once at the end - more efficient for large datasets
                resultList.Sort((a, b) =>
                {
                    int timeCompare = a.startTime.CompareTo(b.startTime);
                    return timeCompare != 0 ? timeCompare : a.trackIndex.CompareTo(b.trackIndex);
                });

                AllNotes = resultList.ToArray();
                TotalDuration = AllNotes.Length > 0 ? AllNotes.Max(n => n.endTime) : 0f;

                if (AllNotes.Length > 0)
                {
                    SegmentDuration = TextureLoader.CreateTextureSegments(AllNotes, TotalDuration, OPTIMAL_PIXELS_PER_MS);
                    TextureLoader.PrepareSegmentNoteCache(AllNotes);
                }

                TextureRenderer.ready = true;
            }
            finally
            {
                resultListPool.Return(resultList);
            }
        }
    }

    // Simple object pool implementation for better memory management
    public class ObjectPool<T> where T : class
    {
        private readonly ConcurrentQueue<T> _objects = new();
        private readonly Func<T> _objectGenerator;

        public ObjectPool(Func<T> objectGenerator)
        {
            _objectGenerator = objectGenerator ?? throw new ArgumentNullException(nameof(objectGenerator));
        }

        public T Get()
        {
            return _objects.TryDequeue(out T item) ? item : _objectGenerator();
        }

        public void Return(T item)
        {
            _objects.Enqueue(item);
        }
    }
}